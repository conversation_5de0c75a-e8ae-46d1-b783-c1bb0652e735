import { CURRENCY_UI_TYPE, PNL_ZINDEX } from "../../common/constant/Constant";
import { HeroAction, PLANET_ITEM_ID, PlanetEvent, PlanetMineType, PlanetNodeType, RuleType, UIFunctionType, WeakGuideType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import NodeType from "../../common/event/NodeType";
import { gameHelper } from "../../common/helper/GameHelper";
import { animHelper } from "../../common/helper/AnimHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { unlockHelper } from "../../common/helper/UnlockHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import { localConfig } from "../../common/LocalConfig";
import { WeakGuideObj } from "../../model/guide/WeakGuideModel";
import ConditionObj from "../../model/common/ConditionObj";
import <PERSON><PERSON>heckPointModel from "../../model/planet/PlanetCheckPointModel";
import PlanetMineModel from "../../model/planet/PlanetMineModel";
import PlanetEmptyNode from "../../model/planet/PlanetEmptyNode";
import PlanetTitleCmpt from "../cmpt/planet/PlanetTitleCmpt";
import PlanetWindCtrl from "../planet/PlanetWindCtrl";
import DragCmpt, { DragEvent } from "../cmpt/common/DragCmpt";
import PlanetNodeModel from "../../model/planet/PlanetNodeModel";
import { ExploreInfo } from "../../model/planet/DeepExploreModel";
import DeepExplorePnlCtrl from "../planetEntry/DeepExplorePnlCtrl";
import PlanetQuestionModel from "../../model/planet/PlanetQuestionModel";
import MultiColor from "../../../core/component/MultiColor";
import CurrencyUICmpt from "../cmpt/ui/CurrencyUICmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class PlanetUIPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected rewardTipsNode_: cc.Node = null // path://top/rewardTips_n
    protected titleNode_: cc.Node = null // path://top/title_n
    protected debugCheckPointsSv_: cc.ScrollView = null // path://top/debugCheckPoints_sv
    protected uiCommonNode_: cc.Node = null // path://ui_common_wg_n
    protected controlNode_: cc.Node = null // path://bottom/control_n
    protected skipNode_: cc.Node = null // path://bottom/control_n/skip_be_n
    protected itemsNode_: cc.Node = null // path://bottom/items_n
    protected backToPlanetNode_: cc.Node = null // path://bottom/layout/back/backToPlanet_be_n
    protected expTimeLbl_: cc.Label = null // path://bottom/layout/back/backToPlanet_be_n/expTime_l
    protected backToEntryNode_: cc.Node = null // path://bottom/layout/back/backToEntry_be_n
    protected toolNode_: cc.Node = null // path://bottom/layout/New Node/tool_be_n
    protected blessNode_: cc.Node = null // path://bottom/bless_n
    protected taskNode_: cc.Node = null // path://task_wg_n
    protected backNode_: cc.Node = null // path://back_be_n
    protected questionEnergyNode_: cc.Node = null // path://questionEnergy_n
    protected topLayerNode_: cc.Node = null // path://topLayer_n
    protected flutterNode_: cc.Node = null // path://flutter_n
    protected tvNode_: cc.Node = null // path://tv_n
    //@end

    private touchId: number = -1
    private exploreInfo: ExploreInfo = null

    public listenEventMaps() {
        return [
            { [EventType.PLANET_NODE_COMPLETE]: this.onNodeComplete },
            { [EventType.HERO_CHANGE_STATE]: this.onStateChange },
            { [EventType.PLAENT_CONTROL_TIP_CHANGE]: this.setControlTip },
            { [EventType.CLAIM_MINE_REWARD_END]: this.playRewardAnim },
            { [EventType.PLANET_NODE_COMPLETE]: this.updateProgress },
            { [NodeType.PLANET_UI_CURRENCY_ICON]: (type: string) => this.Child(`currency_layout/${CURRENCY_UI_TYPE[type]}/icon`) },
            { [EventType.GUIDE_UNLOCK_FUNTION]: this.onFunctionUnlock },
            { [NodeType.PLANET_ITEM_ICON]: this.getEmptyItemIcon },
            { [EventType.READY_ADD_PLANET_ITEM]: this.addEmptyItem },
            { [EventType.ADD_PLANET_ITEM]: this.updateItems },
            { [EventType.REMOVE_PLANET_ITEM]: this.updateItems },
            { [EventType.PLAENT_CAN_CONTROL_JUMP]: this.updateControlGray, tag: "create" },
            { [EventType.GET_UI_FUNCTION_NODE]: this.getFunctionNode },
            { [EventType.TARGET_PLANET_NODE]: this.onTargetNode },
            { [EventType.SHOW_WEAK_GUIDE]: this.onWeakGuide },
            { [EventType.PLANET_DEEP_EXPLORE_START]: this.onExploreStart },
            { [EventType.PLANET_DEEP_EXPLORE_END]: this.onExploreEnd },
            { [EventType.PLANET_NODE_END]: this.updateToolBless },
            { [EventType.UPDATE_CURRENCY]: this.updateToolBless },
            { [EventType.SHOW_CURRENCY_UI]: this.updateCurrenyUI },
            { [EventType.SHOW_FLUTTER_MONEY]: this.onShowFlutterMoney },
            { [EventType.PLANET_JUMP_SHOW_TV]: this.showTv() },

            //guide node
            { [NodeType.GUIDE_BUTTON_BACK_1]: () => this.backNode_ },
            { [NodeType.GUIDE_BUTTON_INTERACT]: () => this.controlNode_ },
            { [NodeType.GUIDE_BUTTON_ITEM]: this.getItemIcon },
            { [NodeType.GUIDE_BUTTON_TOOL]: () => { return this.getFunctionNode(UIFunctionType.TOOL) } },
            { [NodeType.GUIDE_BUTTON_BACK_8]: () => { return this.backToEntryNode_ } },
            { [NodeType.GUIDE_PLANET_AREA]: () => { return this.backToPlanetNode_ } },
            { [NodeType.PLANET_UI_TOPLAYER]: () => { return this.topLayerNode_ } },
            { [NodeType.PLANET_UI_BLESS_ICON]: () => { return this.blessNode_.getChildByName("icon") } },
        ]
    }

    public async onCreate() {
        this.setParam({ adaptWidth: 0, isMask: false, isAct: false, Index: PNL_ZINDEX.UI })

        let hero = gameHelper.hero
        this.controlNode_.on(cc.Node.EventType.TOUCH_START, (event: cc.Event.EventTouch) => {
            if (!this.controlNode_.Component(cc.Button).interactable) {
                return
            }
            if (this.touchId !== -1) {
                return
            }
            this.touchId = event.touch.getID()
            this.updateControlBtnPress(true)
            eventCenter.emit(EventType.PLAENT_CONTROL_TOUCH_START)
        }, this)

        this.controlNode_.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {
            if (this.touchId !== event.getID()) {
                return
            }
            this.touchId = -1
            this.updateControlBtnPress(false)
            eventCenter.emit(EventType.PLAENT_CONTROL_TOUCH_END)
            hero.onNextAction()
        }, this)

        this.controlNode_.on(cc.Node.EventType.TOUCH_CANCEL, (event: cc.Event.EventTouch) => {
            if (event && this.touchId !== event.getID()) {
                return
            }
            this.touchId = -1
            this.updateControlBtnPress(false)
            eventCenter.emit(EventType.PLAENT_CONTROL_TOUCH_CANCEL)
        }, this)

        // this.uiCommonNode_.Component(UICommonWdtCtrl).setBagNeedAct(true)
    }

    public onEnter(data: any) {
        this.showTv(false)
        this.updateProgress()

        this.initDebugCheckPoint()

        this.updateControlBtn()
        this.updateControlBtnPress()

        this.updateItems()

        this.debugCheckPointsSv_.node.active = localConfig.debug && gameHelper.guide.logic.isPlanetWind()

        unlockHelper.initFunctionNode(this.getFunctionNode.bind(this))

        eventCenter.emit(EventType.HIDE_MENU)

        let planet = gameHelper.planet.getCurPlanet()
        let curMap = planet.getBranchCurMap()
        if (this.toolNode_.active) {
            this.toolNode_.active = gameHelper.guide.logic.isPlanetWind() && curMap.getNodes().some(n => n.nodeType == PlanetNodeType.MINE)
        }

        switch (mc.currWindName) {
            case "planetQuestion":
                this.questionEnergyNode_.active = true
                this.backToEntryNode_.active = false
                this.uiCommonNode_.active = false
                this.taskNode_.active = false
                break
            default:
                this.backToEntryNode_.active = gameHelper.guide.logic.isPlanetWind()
                this.questionEnergyNode_.active = false
                this.uiCommonNode_.active = true
                this.taskNode_.active = true
        }

        this.backNode_.active = gameHelper.guide.logic.isPlanetEntry() || gameHelper.guide.logic.isPlanetWind()

        this.updateBackToPlanetNode()
        this.updateBackParent()

        this.exploreInfo = gameHelper.deepExplore.getExploreInfo(planet.getId())

        this.updateExpInfo()

        this.updateToolBless()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://back_be_n
    onClickBack(event: cc.Event.EventTouch, data: string) {
        // 如果是在探索界面
        if (gameHelper.guide.logic.isPlanetWind() && gameHelper.planet.getCurPlanet().isUnlockEntry()) {
            viewHelper.gotoPlanetEntry()
        }
        else {
            viewHelper.gotoMainFromPlanet()
        }
    }

    // path://bottom/layout/New Node/tool_be_n
    onClickTool(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl('tool/ToolMakePnl', gameHelper.weakGuide.ToolMakeData)
    }

    // path://bottom/control_n/skip_be_n
    onClickSkip(event: cc.Event.EventTouch, data: string) {
        this.skip()
    }

    // path://bottom/layout/back/backToEntry_be_n
    onClickBackToEntry(event: cc.Event.EventTouch, data: string) {
        // 直接返回列车
        viewHelper.gotoMainFromPlanet()
    }

    // path://bottom/layout/back/backToPlanet_be_n
    onClickBackToPlanet(event: cc.Event.EventTouch, data: string) {
        let planet = gameHelper.planet.getCurPlanet()
        if (planet.isDone()) {
            if (this.exploreInfo) {
                viewHelper.showPnl("planetEntry/DeepExplorePnl")
            }
            else {
                viewHelper.showPnl("planetEntry/PlanetAreaDeepPnl")
            }
        }
        else {
            if (planet.canGotoPlanet()) {
                viewHelper.showPnl("planetEntry/PlanetAreaPnl")
            }
            else {
                viewHelper.showAlert("这里的敌人还太危险，先探索其他星球吧")
            }
        }
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    update() {
        if (!this.exploreInfo || this.exploreInfo.isFinish()) {
            this.expTimeLbl_.node.active = false
            return
        }
        this.expTimeLbl_.node.active = true
        this.expTimeLbl_.string = this.exploreInfo.getTimeStr()
    }


    private updateExpInfo() {
        const df = this.backToPlanetNode_.Child("default")
        const sk = this.backToPlanetNode_.Child("sk", sp.Skeleton)
        if (!gameHelper.planet.getCurPlanet().isDone()) {
            df.active = true
            sk.node.active = false
            return
        }
        df.active = false

        sk.node.active = true
        if (this.exploreInfo) {
            sk.playAnimation(this.exploreInfo.isFinish() ? "wancheng" : "tansuo", true)
            return
        }
        sk.playAnimation("tansuo_daiji", true)
    }

    private async skip() {
        let model = gameHelper.planet.getCurPlanet().getBranchCurMap().getCurNode()
        if (gameHelper.hero.target != model) return
        gameHelper.hero.isSkip = true
        if (model instanceof PlanetMineModel) {
            await model.die()
            gameHelper.hero.collectEnd()
        }
        else if (model instanceof PlanetCheckPointModel) {
            await model.syncDie()
            model.die()
            model.end()
            gameHelper.hero.battleEnd()
        }
        else if (model instanceof PlanetQuestionModel) {
            await model.syncDie()
            model.die()
            model.end()
        }
        else {
            model.die()
        }
        model.isEnd = true
    }


    public getFunctionNode(type: UIFunctionType) {
        switch (type) {
            case UIFunctionType.PLANET_BACK:
                return this.backNode_
            case UIFunctionType.TOOL:
                return this.toolNode_
        }
    }

    private onNodeComplete() {
        this.updateProgress()
    }

    private updateProgress() {
        let planet = gameHelper.planet.getCurPlanet()
        let percent = planet.getPercent()
        let title = planet.name
        let progress = ""
        if (gameHelper.guide.logic.isPlanetWind()) {
            let map = planet.getBranchCurMap()
            let branch = map.getBranch()
            if (branch) {
                percent = branch.getPercent()
                title = branch.name || title
            }
            else {
                let area = map.getArea()
                percent = area.getPercent()
                title = area.name || title
            }
            progress = assetsMgr.lang("explore_guiText_1", `${Math.floor(percent * 100)}`)
        }
        else {
            progress = assetsMgr.lang(planet.entryName)
        }
        this.titleNode_.Child('PlanetTitle').Component(PlanetTitleCmpt).init(title, progress, RuleType.NONE)
    }

    private initDebugCheckPoint() {
        let checkPoints = gameHelper.planet.getCurPlanet().getBranchNodes().filter(m => m instanceof PlanetCheckPointModel)
        this.debugCheckPointsSv_.Items(checkPoints, (node, data) => {
            node.Child("Background/Label").Component(cc.Label).string = data.getId()
            node.on("click", () => {
                this.emit(EventType.ENTER_BATTLE, data, { debug: true })
            })
        })
    }

    private updateControlBtnPress(down?: boolean) {
        let controlNode = this.controlNode_
        let press = controlNode.Child("press")
        press.y = 27
        if (down) {
            press.y -= 10
        }
    }

    private updateControlGray(canClick: boolean) {
        this.controlNode_.setGray(!canClick)
        this.controlNode_.Component(cc.Button).interactable = canClick
        this.updateControlBtnPress(!canClick)
    }

    private onStateChange() {
        this.updateControlBtn()
    }

    private updateControlBtn() {

        let controlNode = this.controlNode_

        if (!(mc.currWind instanceof PlanetWindCtrl)) {
            controlNode.active = false
            return
        }

        let hero = gameHelper.hero
        let action = hero.getAction()
        let planet = gameHelper.planet.getCurPlanet()
        let map = planet.getBranchCurMap()
        let curNode = map.getCurNode()
        let nodes = planet.getBranchNodes()
        if ((
            action == HeroAction.COLLECT_END || action == HeroAction.WAIT_TO_COLLECT || action == HeroAction.COLLECT
            || action == HeroAction.WAIT_TO_BATTLE || action == HeroAction.JUMP_GAME || action == HeroAction.WAIT_TO_QUESTION
            || action == HeroAction.WAIT_TO_RAGE_MODE || action == HeroAction.JUMP_GAME3
            || (action == HeroAction.MOVE && !(curNode instanceof PlanetEmptyNode) && curNode != nodes[0])) && !gameHelper.hero.isRageMode() && !gameHelper.hero.isSkiMode()) { //排除第一个节点和特殊节点
            controlNode.active = true
            this.setControlIcon(action)
            this.setControlTip(true)
        }
        else {
            controlNode.active = false
            this.updateControlGray(true)
            this.touchId = -1
        }
    }

    private setControlIcon(action: HeroAction) {
        let icon = this.controlNode_.Child("press/icon")
        let index = 0
        let controll = 0
        let press = 0
        if (action == HeroAction.WAIT_TO_COLLECT || action == HeroAction.COLLECT) {
            let target = gameHelper.hero.getCollectTarget()
            switch (target.type) {
                case PlanetMineType.TREE:
                    index = 0;
                    break
                case PlanetMineType.ORE:
                    index = 1;
                    break
                case PlanetMineType.PART:
                    index = 2;
                    break
                case PlanetMineType.SEED:
                    index = 6;
                    break
            }
        }
        else if (action == HeroAction.WAIT_TO_BATTLE) {
            index = 3
        }
        else if (action == HeroAction.JUMP_GAME) {
            index = 4
        }
        else if (action == HeroAction.COLLECT_END || action == HeroAction.MOVE) {
            index = 5
        }
        else if (action == HeroAction.WAIT_TO_QUESTION) {
            index = 7
        }
        else if (action == HeroAction.WAIT_TO_RAGE_MODE) {
            index = 8
        }
        else if (action == HeroAction.JUMP_GAME3) {
            index = 9
            controll = 1
            press = 1
        }
        icon.Component(cc.MultiFrame).setFrame(index)
        this.controlNode_.Component(cc.MultiFrame).setFrame(controll)
        this.controlNode_.Child("press").Component(cc.MultiFrame).setFrame(press)
        this.skipNode_.active = localConfig.debug
    }

    private updateBackToPlanetNode() {
        let succ = false
        let planet = gameHelper.planet.getCurPlanet()
        if (gameHelper.guide.logic.isPlanetEntry()) {
            if (planet.isDone()) {
                succ = unlockHelper.isUnlockFunction(UIFunctionType.DEEP_EXPLORE)
            }
            else {
                succ = true
            }
        }
        this.backToPlanetNode_.active = succ
        this.updateBackParent()
    }

    private updateBackParent() {
        this.backToPlanetNode_.parent.active = this.backToEntryNode_.active || this.backToPlanetNode_.active
    }

    private setControlTip(delay: boolean = false) {
        let bol = false
        let action = gameHelper.hero.getAction()
        if (action == HeroAction.JUMP_GAME) {
            if (!gameHelper.hero.hasJump) {
                if (delay) {
                    this.delayControlTip()
                }
                else {
                    bol = true
                }
            }
        }
        this.controlNode_.Child("tip").active = bol
    }

    private async delayControlTip() {
        await ut.wait(0, this)
        this.setControlTip()
    }

    private playRewardAnim(target: cc.Node) {
        animHelper.scaleBag(target)
    }

    private onFunctionUnlock(type: UIFunctionType) {
        unlockHelper.unlockFunction(this.getFunctionNode.bind(this), type)
        this.updateBackToPlanetNode()
    }

    private addEmptyItem(num = 1) {
        for (let i = 0; i < num; i++) {
            this.itemsNode_.AddItem(null, (it) => {
                it.Child('icon', cc.Sprite).spriteFrame = null
            })
        }
        this.itemsNode_.Component(cc.Layout).updateLayout()
    }

    private updateItems() {
        let cfgs = [
            { id: PLANET_ITEM_ID.CLOCKWORK, icon: "ty_hd_05", scale: 0.5, show: this.checkClockworkShow },
            { id: PLANET_ITEM_ID.BLACK_HOLE_KEY, icon: "yd_daoju_2", show: this.checkBlackHoleKeyShow },
            { id: PLANET_ITEM_ID.ORE_KEY_3, icon: "wk_jiemi1", show: () => this.checkOreKeyShow(3) },
            { id: PLANET_ITEM_ID.ORE_KEY_1, icon: "wk_jiemi2", show: () => this.checkOreKeyShow(1) },
            { id: PLANET_ITEM_ID.ORE_KEY_4, icon: "wk_jiemi3", show: () => this.checkOreKeyShow(4) },
            { id: PLANET_ITEM_ID.ORE_KEY_2, icon: "wk_jiemi4", show: () => this.checkOreKeyShow(2) },
            { id: PLANET_ITEM_ID.INSTANCE_KEY_1, icon: "fb_open_shitou_an", show: this.checkInstanceKey1Show },
        ]
        let tag = this.getTag()

        cfgs = cfgs.filter(c => c.show.call(this))

        this.itemsNode_.Items(cfgs, (it, cfg) => {
            let url = `planet/item/${cfg.icon}`
            resHelper.loadTmpIcon(url, it.Child('icon', cc.Sprite), tag)

            let icon = it.Child("icon")
            icon.scale = 1
            if (cfg.scale) {
                icon.scale = cfg.scale
                icon.Component(DragCmpt).zoomInit()
            }

            icon.off(DragEvent.CLICK)
            icon.on(DragEvent.CLICK, () => {
                viewHelper.showAlert("explore_tips_3")
            })
            icon.off(DragEvent.DRAG_END)
            icon.on(DragEvent.DRAG_END, (screenPos: cc.Vec2) => {
                let succ = eventCenter.get(EventType.CHECK_USE_PLANET_ITEM, cfg.id, screenPos)
                if (succ) {
                    eventCenter.emit(EventType.USE_PLANET_ITEM, cfg.id, screenPos)
                }
                else {
                    viewHelper.showAlert("explore_tips_10")
                }
            })

        })
        this.itemsNode_.Component(cc.Layout).updateLayout()
    }

    private getItemIcon(index: number = 0) {
        let nodes = this.itemsNode_.children.filter(c => c.active)
        let node = nodes[index]
        if (!node) return
        return node.Child("icon")
    }

    private getEmptyItemIcon(index: number = 0) {
        let nodes = this.itemsNode_.children.filter(c => c.active && c.Child("icon", cc.Sprite).spriteFrame == null)
        return nodes[index].Child("icon")
    }

    private checkClockworkShow() {
        let startNode = gameHelper.planet.getNodeByEvent(PlanetEvent.CLOCKWORK)
        let endNode: PlanetNodeModel = gameHelper.planet.getNodeByEvent(PlanetEvent.SAVE_CAT)
        return startNode.isPass() && !(endNode.isPass() || endNode["__useClock"])
    }

    private checkBlackHoleKeyShow() {
        let startNode = gameHelper.planet.getNodeByEvent(PlanetEvent.BLACK_HOLE_KEY)
        let isEnd = unlockHelper.isUnlockFuncByMisc(UIFunctionType.PLAY_BLACKHOLE)
        return startNode?.isPass() && !isEnd
    }

    private checkOreKeyShow(index: number) {
        if (unlockHelper.isUnlockFunction(UIFunctionType.PLAY_ORE)) return
        let startNode = gameHelper.planet.getNodeByEvent(PlanetEvent.HOLE_AFTER)
        if (!startNode?.isPass()) return false
        return !gameHelper.ore.getPuzzleKey(index)
    }

    private checkInstanceKey1Show() {
        if (unlockHelper.isUnlockFunction(UIFunctionType.PLAY_INSTANCE)) return
        let startNode = gameHelper.planet.getNodeByEvent(PlanetEvent.STAR_FRAG)
        return startNode?.isPass()
    }

    //-----------------------------------------------------------------------------------------------

    private onTargetNode() {
    }

    private onWeakGuide(guide: WeakGuideObj) {
        if (guide.id == WeakGuideType.PLANET_CONTROL_1) {
            let targetNode = this.controlNode_
            animHelper.showWeakGuideFinger(targetNode.parent, guide.fingerGuide, targetNode.getPosition())
            this.checkOverGotoControl1()
        }
        else if (guide.id == WeakGuideType.TOOL_MAKE_1) {
            let targetNode = this.toolNode_
            if (targetNode.active) {
                animHelper.showWeakGuideFinger(targetNode.parent, guide.fingerGuide, targetNode.getPosition())
                this.checkOverGotoTool1()
            }
        }
        else if (guide.id == WeakGuideType.BACK_TO_PLANET_1) {
            let targetNode = this.backToPlanetNode_
            animHelper.showWeakGuideFinger(targetNode.parent, guide.fingerGuide, targetNode.getPosition())
            this.checkOverGotoBackToPlanet1()
        }
    }
    private async checkOverGotoControl1() {
        await viewHelper.waitAnyClickEnd()
        gameHelper.weakGuide.planetControlStep = 0
    }
    private async checkOverGotoTool1() {
        await viewHelper.waitAnyClickEnd()
        gameHelper.weakGuide.ToolMakeStep = 0
        gameHelper.weakGuide.ToolMakeData = null
    }
    private async checkOverGotoBackToPlanet1() {
        await viewHelper.waitAnyClickEnd()
        gameHelper.weakGuide.backToPlanetStep = 0
    }

    private onExploreStart(exploreInfo: ExploreInfo) {
        this.exploreInfo = exploreInfo
        this.updateExpInfo()
    }

    private onExploreEnd() {
        this.exploreInfo = null
        this.updateExpInfo()
    }

    private updateCurrenyUI(type, show) {
        let node = this.Child(`currency_layout/${CURRENCY_UI_TYPE[type]}`)
        node.Component(CurrencyUICmpt).show(show)
    }

    private updateToolBless() {
        this.blessNode_.active = gameHelper.tool.isBless() || gameHelper.hero.isRageMode()
        if (this.blessNode_.active) {
            let progress = this.blessNode_.Child("progress", cc.Sprite)
            let index = 0
            if (gameHelper.tool.isBless()) {
                index = 1
                progress.fillRange = gameHelper.tool.getBlessCount() / gameHelper.tool.getMaxBlessCount()
            }
            else {
                progress.fillRange = 1 - gameHelper.hero.getRageModeProgress()
            }
            progress.Component(MultiColor).setColor(index)
            this.blessNode_.Child("icon", cc.MultiFrame).setFrame(index)
        }
    }

    private onShowFlutterMoney(cond: ConditionObj, worldPos: cc.Vec2, offsetY = 0) {
        let node = this.flutterNode_
        let mainCamera = cc.find("Canvas/PlanetCamera").getComponent(cc.Camera)
        let _temp_vec2 = cc.v2()
        let targetCamera = cc.Camera.findCamera(node)
        mainCamera.getWorldToScreenPoint(worldPos, _temp_vec2)
        targetCamera.getScreenToWorldPoint(_temp_vec2, _temp_vec2);
        let location = node.convertToNodeSpaceAR(_temp_vec2, _temp_vec2)
        location.y += offsetY
        animHelper.playFlutterMoney(cond, node, location, this.getTag())
    }

    private showTv(is: boolean = true) {
        this.tvNode_.active = is
    }

}

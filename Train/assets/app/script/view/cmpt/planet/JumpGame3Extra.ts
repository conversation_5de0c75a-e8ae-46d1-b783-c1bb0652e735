import { error } from "../../../../../../tool/localize/src/panel"
import MountPointCmpt from "../common/MountPointCmpt"

export const HERO_FOOT_WIDTH = 20 // Hero脚部碰撞宽度（左右各延伸20px）
export enum LandType {
    None = "none",
    Normal = "normal",
    Die = "die",
    Water = "water",
    Rebirth = "rebirth",
    Shadow = "shadow",
}

// Hero轨迹信息
export interface HeroTrajectory {
    // 左脚线段位置
    leftFoot: { start: cc.Vec2, end: cc.Vec2 }
    // 右脚线段位置
    rightFoot: { start: cc.Vec2, end: cc.Vec2 }
    // 世界矩形
    worldPoints: cc.Vec2[]
    offset: cc.Vec2
}

// 碰撞结果
export interface CollisionResult {
    point: cc.Vec2      // 碰撞点
    distance: number    // 碰撞距离（用于选择最近碰撞）
    normal?: cc.Vec2    // 碰撞法线（可选）
}

export interface LandCollider extends cc.Component {
    type: LandType
    node: cc.Node
    index: number

    intersectWith: (trajectory: HeroTrajectory) => CollisionResult | null
    isNeedHeroCollider(): boolean
    getLeftMax(): number
    getRightMax(): number
}

export class BaseLandCollider extends cc.Component implements LandCollider {
    type: LandType = LandType.None
    node: cc.Node = null
    index: number = 0

    public intersectWith(_trajectory: HeroTrajectory): CollisionResult | null { return null }

    public isNeedHeroCollider(): boolean { return false }
    public getLeftMax(): number { throw new Error("Method not implemented.") }
    public getRightMax(): number { throw new Error("Method not implemented.") }
}

export class NormalLandCollider extends BaseLandCollider {
    type: LandType = LandType.Normal

    private lineNode: cc.Node = null
    private startPos: cc.Vec2
    private endPos: cc.Vec2

    onLoad() {
        this.reset()
    }

    public reset() {
        this.lineNode = this.node.Child("line")
        if (this.lineNode) {
            this.startPos = ut.convertToNodeAR(this.lineNode, this.node.parent.parent, cc.v2(-this.lineNode.anchorX * this.lineNode.width, 0))
            this.endPos = ut.convertToNodeAR(this.lineNode, this.node.parent.parent, cc.v2((1 - this.lineNode.anchorX) * this.lineNode.width, 0))
        }
    }

    public intersectWith(trajectory: HeroTrajectory): CollisionResult | null {
        if (!this.lineNode) {
            console.error(`NormalLandCollider 检测失败,因为不存在可停留线段,name:${this.node.name}`)
            return null
        }
        const intersectPos = cc.v2()
        const trajectories = [trajectory.rightFoot, trajectory.leftFoot]
        for (let i = 0; i < trajectories.length; i++) {
            const traj = trajectories[i]
            if (ut.lineLine(traj.start, traj.end, this.startPos, this.endPos, intersectPos)) {
                if (!intersectPos.fuzzyEquals(traj.start, 0.01)) {
                    intersectPos.x += i == 0 ? -HERO_FOOT_WIDTH : HERO_FOOT_WIDTH
                    return {
                        point: intersectPos,
                        distance: traj.start.sub(intersectPos).mag()
                    }
                }
            }
        }
        return null
    }

    public getLeftMax(): number {
        return this.startPos.x
    }

    public getRightMax(): number {
        return this.endPos.x
    }
}

// 重生地块
export class RebirthLandCollider extends NormalLandCollider {
    type: LandType = LandType.Rebirth

    public intersectWith(trajectory: HeroTrajectory): CollisionResult | null {
        return super.intersectWith(trajectory)
    }
}

// hero死亡后的影子
export class HeroShowLandCollider extends NormalLandCollider {
    type: LandType = LandType.Shadow

    public intersectWith(_trajectory: HeroTrajectory): CollisionResult | null {
        return super.intersectWith(_trajectory)
    }
}

// 瀑布地块
export class WaterLandCollider extends BaseLandCollider {
    type: LandType = LandType.Water
    interval: number = 0
    duration: number = 0
    worldPoints: cc.Vec2[] = null
    boxNode: cc.Node = null
    orgBoxPoints: cc.Vec2[] = null
    enterMountPoint: cc.Node = null
    exitMountPoint: cc.Node = null

    private timer: number = 0
    private isPlaying: boolean = false

    onLoad() {
        const name = this.node.name
        const arg = name.split("_")
        if (arg.length == 3) {
            this.interval = Math.max(1, Number(arg[1])) * ut.Time.Second
            this.duration = Math.max(1, Number(arg[2])) * ut.Time.Second
        }
        else {
            this.interval = 1 * ut.Time.Second
            this.duration = 1 * ut.Time.Second
        }
        const sk = this.node.Component(sp.Skeleton)
        sk.playAnimation("jingzhi", true)
        const collider = this.node.getComponent(cc.PolygonCollider)
        if (!collider) {
            throw new Error(`WaterLandCollider 检测失败,因为不存在碰撞器,name:${this.node.name}`)
        }
        this.worldPoints = collider.points.map(point => {
            const localPoint = cc.v2(point.x + collider.offset.x, point.y + collider.offset.y)
            return this.node.convertToWorldSpaceAR(localPoint)
        })
        const mount = this.node.Component(MountPointCmpt)
        this.boxNode = mount.getPoint("box")
        this.enterMountPoint = mount.getPoint("guadian_enter")
        this.exitMountPoint = mount.getPoint("guadian_exit")
        if (!this.boxNode || !this.enterMountPoint || !this.exitMountPoint) {
            throw new Error(`WaterLandCollider 检测失败,因为不存在挂点,name:${this.node.name}`)
        }
        const points = this.boxNode.Component(cc.BoxCollider).world.points
        this.orgBoxPoints = points.map(point => this.node.convertToWorldSpaceAR(point))
    }

    update(dt: number) {
        this.timer += dt * ut.Time.Second
        if (!this.isPlaying && this.timer >= this.interval) {
            this.playLoopAnimation()
        }
    }

    private async playLoopAnimation() {
        this.isPlaying = true
        const sk = this.node.Component(sp.Skeleton)
        await sk.playAnimation("enter", false)
        sk.playAnimation("loop", true)
        await new Promise(resolve => {
            this.scheduleOnce(resolve, this.duration / 1000)
        })
        await sk.playAnimation("exit", false)
        sk.playAnimation("jingzhi", true)
        this.isPlaying = false
        this.timer = 0
    }

    private changeMountPoint(parent: cc.Node) {
        this.boxNode.parent = parent
        const isEnter = parent == this.enterMountPoint
        this.boxNode.anchorY = isEnter ? 1 : 0
    }

    private getBoxColliderPoints(): cc.Vec2[] {


    }

    public intersectWith(trajectory: HeroTrajectory): CollisionResult | null {
        if (!this.isPlaying) return null
        if (!cc.Intersection.polygonPolygon(trajectory.worldPoints, this.worldPoints)) {
            return null
        }
        return { point: trajectory.leftFoot.end, distance: 1 }
    }

    public isNeedHeroCollider(): boolean { return true }
}

// 一碰就死地块
export class DieLandCollider extends BaseLandCollider {
    type: LandType = LandType.Die
    worldPoints: cc.Vec2[] = null
    leftXAr: number
    rightXAr: number
    topYAr: number

    onLoad(): void {
        this.leftXAr = ut.convertToNodeAR(this.node, this.node.parent.parent, cc.v2(-this.node.anchorX * this.node.width, this.node.height)).x
        this.rightXAr = this.leftXAr + this.node.width

        const collider = this.node.getComponent(cc.PolygonCollider)
        if (!collider) {
            throw new Error(`DieLandCollider 检测失败,因为不存在碰撞器,name:${this.node.name}`)
        }
        this.worldPoints = collider.points.map(point => {
            const localPoint = cc.v2(point.x + collider.offset.x, point.y + collider.offset.y)
            return this.node.convertToWorldSpaceAR(localPoint)
        })
        const top = collider.points[0].y
        // 这个转换有个问题 就是sprite有光晕导致的无法trimmed出现空白 无法使用节点的height 会让影子出现“悬空”
        // 所以直接使用碰撞盒子的第一个节点的y 前提是保证有碰撞盒子，并且y拉对了
        this.topYAr = ut.convertToNodeAR(this.node, this.node.parent.parent, cc.v2(0, top)).y
    }

    public intersectWith(trajectory: HeroTrajectory): CollisionResult | null {
        if (cc.Intersection.polygonPolygon(trajectory.worldPoints, this.worldPoints)) {
            let point = null
            // 特殊处理上方 因为采样更新会让hero位置不可控，影子可能恰好落在地块上，也可能高一点点
            // 这里处理一下，一律落在地块最上方
            if (trajectory.leftFoot.start.x >= this.leftXAr || trajectory.rightFoot.end.x >= this.leftXAr) {
                if (trajectory.rightFoot.start.x <= this.rightXAr || trajectory.rightFoot.end.x <= this.rightXAr) {
                    point = cc.v2(trajectory.leftFoot.end.x + HERO_FOOT_WIDTH, this.topYAr)
                }
            }
            return { point, distance: 1 }
        }
        return null
    }

    public getLeftMax(): number {
        return this.node.x - this.node.width >> 1
    }
    public getRightMax(): number {
        return this.node.x + this.node.width >> 1
    }
    public isNeedHeroCollider(): boolean { return true }
}

export class LandColliderFactory {
    private static colliderMap = new Map<LandType, new () => LandCollider>([
        [LandType.Normal, NormalLandCollider],
        [LandType.Shadow, HeroShowLandCollider],
        [LandType.Die, DieLandCollider],
        [LandType.Water, WaterLandCollider],
        [LandType.Rebirth, RebirthLandCollider],
    ])

    static createCollider(point: cc.Node): LandCollider {
        let landType = this.determineLandType(point)
        const ColliderClass = this.colliderMap.get(landType) || NormalLandCollider
        const cmpt = point.addComponent(ColliderClass)
        return cmpt
    }

    private static determineLandType(point: cc.Node): LandType {
        const nodeName = point.name.toLowerCase()
        if (nodeName.includes(LandType.Shadow)) return LandType.Shadow
        if (nodeName.includes(LandType.Die)) return LandType.Die
        if (nodeName.includes(LandType.Water)) return LandType.Water
        if (nodeName.includes(LandType.Rebirth)) return LandType.Rebirth

        return LandType.Normal
    }
}


pix_jump_shui.png
size: 1253,198
format: RGBA8888
filter: Linear,Linear
repeat: none
cks1
  rotate: false
  xy: 1123, 25
  size: 128, 84
  orig: 128, 84
  offset: 0, 0
  index: -1
cks2
  rotate: false
  xy: 1123, 111
  size: 128, 85
  orig: 128, 85
  offset: 0, 0
  index: -1
d_2_1
  rotate: false
  xy: 731, 6
  size: 390, 190
  orig: 390, 190
  offset: 0, 0
  index: -1
shui1
  rotate: true
  xy: 2, 70
  size: 126, 727
  orig: 126, 727
  offset: 0, 0
  index: -1
shui1_1
  rotate: true
  xy: 1123, 17
  size: 6, 33
  orig: 6, 33
  offset: 0, 0
  index: -1
shui1_2
  rotate: false
  xy: 25, 32
  size: 27, 36
  orig: 27, 36
  offset: 0, 0
  index: -1
shui1_3
  rotate: false
  xy: 80, 38
  size: 24, 30
  orig: 24, 30
  offset: 0, 0
  index: -1
shui1_4
  rotate: false
  xy: 2, 25
  size: 21, 43
  orig: 21, 43
  offset: 0, 0
  index: -1
shui1_5
  rotate: false
  xy: 106, 38
  size: 24, 30
  orig: 24, 30
  offset: 0, 0
  index: -1
shui1_6
  rotate: false
  xy: 54, 32
  size: 24, 36
  orig: 24, 36
  offset: 0, 0
  index: -1
shui1_7
  rotate: false
  xy: 2, 2
  size: 6, 21
  orig: 6, 21
  offset: 0, 0
  index: -1
